import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_colors.dart';

/// A specialized text field for mileage input
class MileageInput extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final bool isRequired;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final String? hintText;
  final bool enabled;

  const MileageInput({
    super.key,
    required this.label,
    required this.controller,
    this.isRequired = false,
    this.validator,
    this.onChanged,
    this.hintText,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label
          RichText(
            text: TextSpan(
              text: label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: enabled ? AppColors.textPrimary : AppColors.textDisabled,
              ),
              children: [
                if (isRequired)
                  const TextSpan(
                    text: ' *',
                    style: TextStyle(color: AppColors.error),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          // Text field
          TextFormField(
            controller: controller,
            enabled: enabled,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(
                10,
              ), // Reasonable limit for mileage
            ],
            decoration: InputDecoration(
              hintText: hintText ?? 'Enter mileage (km)',
              prefixIcon: const Icon(Icons.speed),
              suffixText: 'km',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.primary,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.error),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.error, width: 2),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: AppColors.border.withOpacity(0.5),
                ),
              ),
              filled: true,
              fillColor: enabled ? Colors.white : Colors.grey[50],
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            validator: validator ?? (isRequired ? _defaultValidator : null),
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  /// Default validator for required fields
  String? _defaultValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '$label is required';
    }

    // Try to parse as integer
    final intValue = int.tryParse(value.trim());
    if (intValue == null) {
      return 'Please enter a valid number';
    }

    // Check if value is positive
    if (intValue <= 0) {
      return '$label must be greater than 0';
    }

    // Check reasonable range (0 to 9,999,999 km)
    if (intValue > 9999999) {
      return '$label seems too high';
    }

    return null;
  }
}
