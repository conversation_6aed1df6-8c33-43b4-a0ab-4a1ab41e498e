import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_colors.dart';

/// A specialized text field for currency input
class CurrencyInputField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final bool isRequired;
  final bool allowNegative;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final String? hintText;
  final IconData? prefixIcon;

  const CurrencyInputField({
    super.key,
    required this.label,
    required this.controller,
    this.isRequired = false,
    this.allowNegative = false,
    this.validator,
    this.onChanged,
    this.hintText,
    this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label
          RichText(
            text: TextSpan(
              text: label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
              children: [
                if (isRequired)
                  const TextSpan(
                    text: ' *',
                    style: TextStyle(color: AppColors.error),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          // Text field
          TextFormField(
            controller: controller,
            keyboardType: const TextInputType.numberWithOptions(
              decimal: true,
              signed: true,
            ),
            inputFormatters: [
              if (allowNegative)
                FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*'))
              else
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
            ],
            decoration: InputDecoration(
              hintText:
                  hintText ??
                  (allowNegative
                      ? 'Enter amount (can be negative)'
                      : 'Enter amount'),
              prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.border),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.primary,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.error),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.error, width: 2),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            validator: validator ?? (isRequired ? _defaultValidator : null),
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  /// Default validator for required fields
  String? _defaultValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '$label is required';
    }

    // Try to parse as double
    final doubleValue = double.tryParse(value.trim());
    if (doubleValue == null) {
      return 'Please enter a valid number';
    }

    // Check if negative values are allowed
    if (!allowNegative && doubleValue < 0) {
      return '$label cannot be negative';
    }

    return null;
  }
}
