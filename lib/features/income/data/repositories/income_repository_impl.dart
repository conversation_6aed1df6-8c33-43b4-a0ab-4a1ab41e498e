import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart';
import '../../domain/entities/income.dart';
import '../../domain/repositories/income_repository.dart';
import '../datasources/income_local_datasource.dart';
import '../models/income_model.dart';

/// Implementation of IIncomeRepository
class IncomeRepositoryImpl implements IIncomeRepository {
  final IncomeLocalDatasource _localDatasource;

  IncomeRepositoryImpl(this._localDatasource);

  Future<Either<Failure, List<Income>>> getIncomes({
    int limit = 20,
    int offset = 0,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final models = await _localDatasource.getIncomes(
        limit: limit,
        offset: offset,
        startDate: startDate,
        endDate: endDate,
      );

      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  Future<Either<Failure, Income?>> getIncomeById(int id) async {
    try {
      final model = await _localDatasource.getIncomeById(id);
      return Right(model?.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  // Base IRepository methods
  @override
  Future<Either<Failure, List<Income>>> getAll() async {
    try {
      final models = await _localDatasource.getIncomes();
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Income>> getById(int id) async {
    try {
      final model = await _localDatasource.getIncomeById(id);
      if (model == null) {
        return Left(DatabaseFailure('Income with id $id not found'));
      }
      return Right(model.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Income>> save(Income entity) async {
    try {
      final model = IncomeModel.fromEntity(entity);
      final savedModel = await _localDatasource.createIncome(model);
      return Right(savedModel.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, Income>> update(Income entity) async {
    try {
      final model = IncomeModel.fromEntity(entity);
      final updatedModel = await _localDatasource.updateIncome(model);
      return Right(updatedModel.toEntity());
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> delete(int id) async {
    try {
      await _localDatasource.deleteIncome(id);
      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  // IDateRangeRepository methods
  @override
  Future<Either<Failure, List<Income>>> getForDateRange(
    DateTime start,
    DateTime end,
  ) async {
    try {
      final models = await _localDatasource.getIncomes(
        startDate: start,
        endDate: end,
      );
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> checkDateExists(
    DateTime date, {
    int? excludeId,
  }) async {
    try {
      final exists = await _localDatasource.existsForDate(
        date,
        excludeId: excludeId,
      );
      return Right(exists);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  // ISoftDeleteRepository methods
  @override
  Future<Either<Failure, bool>> softDelete(int id) async {
    try {
      await _localDatasource.softDeleteIncome(id);
      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Income>>> getAllIncludingDeleted() async {
    try {
      final models = await _localDatasource.getAllIncludingDeleted();
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> restore(int id) async {
    try {
      await _localDatasource.restoreIncome(id);
      return const Right(true);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  // IIncomeRepository specific methods
  @override
  Future<Either<Failure, List<Income>>> getPaginated({
    int page = 1,
    int limit = 20,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final offset = (page - 1) * limit;
      final models = await _localDatasource.getIncomes(
        limit: limit,
        offset: offset,
        startDate: startDate,
        endDate: endDate,
      );
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getCount({
    DateTime? startDate,
    DateTime? endDate,
    bool includeDeleted = false,
  }) async {
    try {
      final count = await _localDatasource.getCount(
        startDate: startDate,
        endDate: endDate,
        includeDeleted: includeDeleted,
      );
      return Right(count);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, int>> getHighestMileage() async {
    try {
      final mileage = await _localDatasource.getHighestMileage();
      return Right(mileage);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, IncomeSummary>> getSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final models = await _localDatasource.getIncomes(
        startDate: startDate,
        endDate: endDate,
      );

      if (models.isEmpty) {
        return const Right(
          IncomeSummary(
            totalNetIncome: 0,
            recordCount: 0,
            totalRecordCount: 0,
            highestIncome: 0,
            lowestIncome: 0,
            averageIncome: 0,
            totalMileage: 0,
            totalInitialCapital: 0,
            totalFinalResult: 0,
            count: 0,
            averageNetIncome: 0,
            averageMileage: 0,
          ),
        );
      }

      double totalNetIncome = 0;
      double totalInitialCapital = 0;
      double totalFinalResult = 0;
      double totalMileage = 0;
      double highestIncome = double.negativeInfinity;
      double lowestIncome = double.infinity;

      for (final model in models) {
        final netIncome = model.netIncome;
        final mileage = model.mileage.toDouble();

        totalNetIncome += netIncome;
        totalInitialCapital += model.totalInitialCapital.toDouble();
        totalFinalResult += model.totalFinalResult.toDouble();
        totalMileage += mileage;

        if (netIncome > highestIncome) highestIncome = netIncome;
        if (netIncome < lowestIncome) lowestIncome = netIncome;
      }

      final count = models.length;
      final averageIncome = count > 0 ? totalNetIncome / count : 0;
      final averageMileage = count > 0 ? totalMileage / count : 0;

      return Right(
        IncomeSummary(
          totalNetIncome: totalNetIncome,
          recordCount: count,
          totalRecordCount: count,
          highestIncome: highestIncome == double.negativeInfinity
              ? 0.0
              : highestIncome,
          lowestIncome: lowestIncome == double.infinity ? 0.0 : lowestIncome,
          averageIncome: averageIncome.toDouble(),
          totalMileage: totalMileage.toInt(),
          totalInitialCapital: totalInitialCapital,
          totalFinalResult: totalFinalResult,
          count: count.toInt(),
          averageNetIncome: averageIncome.toDouble(),
          averageMileage: averageMileage,
        ),
      );
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Income>>> getForTrends({
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final models = await _localDatasource.getIncomes(
        startDate: startDate,
        endDate: endDate,
        limit: limit ?? 50,
      );
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Income>>> search({
    String? query,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      final models = await _localDatasource.searchIncomes(query ?? '');
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Income>>> getIncomplete() async {
    try {
      final models = await _localDatasource.getIncompleteIncomes();
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<Income>>> getRecent({int limit = 10}) async {
    try {
      final models = await _localDatasource.getIncomes(limit: limit);
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  // ISyncableRepository methods
  @override
  Future<Either<Failure, List<Income>>> getUnsyncedEntities() async {
    try {
      final models = await _localDatasource.getUnsyncedIncomes();
      final entities = models.map((model) => model.toEntity()).toList();
      return Right(entities);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> syncEntities() async {
    try {
      // For now, just mark all unsynced as synced
      // In a real implementation, this would sync with a remote server
      final unsyncedResult = await getUnsyncedEntities();

      return unsyncedResult.fold((failure) => Left(failure), (unsynced) async {
        for (final income in unsynced) {
          if (income.uuid != null) {
            await _localDatasource.markAsSynced(income.uuid!);
          }
        }
        return const Right(true);
      });
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> markAsSynced(String uuid) async {
    try {
      final result = await _localDatasource.markAsSynced(uuid);
      return Right(result);
    } on DatabaseException catch (e) {
      return Left(DatabaseFailure(e.message));
    } catch (e) {
      return Left(DatabaseFailure('Unexpected error: $e'));
    }
  }
}
