import 'package:drift/drift.dart';
import '../../../../core/datasources/app_database.dart';
import '../../../../core/errors/exceptions.dart';
import '../models/income_model.dart';

/// Local data source for income operations using Drift database
class IncomeLocalDatasource {
  final AppDatabase _database;

  IncomeLocalDatasource(this._database);

  /// Get all incomes with pagination
  Future<List<IncomeModel>> getIncomes({
    int limit = 20,
    int offset = 0,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final query = _database.select(_database.incomeTable)
        ..where((tbl) => tbl.isDeleted.equals(false))
        ..orderBy([(t) => OrderingTerm.desc(t.date)])
        ..limit(limit, offset: offset);

      if (startDate != null && endDate != null) {
        query.where((tbl) => tbl.date.isBetweenValues(startDate, endDate));
      }

      final results = await query.get();
      return results.map((row) => IncomeModel.fromDrift(row)).toList();
    } catch (e) {
      throw DatabaseException('Failed to get incomes: $e');
    }
  }

  /// Get income by ID
  Future<IncomeModel?> getIncomeById(int id) async {
    try {
      final query = _database.select(_database.incomeTable)
        ..where((tbl) => tbl.id.equals(id) & tbl.isDeleted.equals(false));

      final result = await query.getSingleOrNull();
      return result != null ? IncomeModel.fromDrift(result) : null;
    } catch (e) {
      throw DatabaseException('Failed to get income by ID: $e');
    }
  }

  /// Check if income exists for date
  Future<bool> existsForDate(DateTime date, {int? excludeId}) async {
    try {
      var query = _database.select(_database.incomeTable)
        ..where((tbl) => tbl.date.equals(date) & tbl.isDeleted.equals(false));

      if (excludeId != null) {
        query = query..where((tbl) => tbl.id.equals(excludeId).not());
      }

      final result = await query.getSingleOrNull();
      return result != null;
    } catch (e) {
      throw DatabaseException('Failed to check if income exists: $e');
    }
  }

  /// Create new income
  Future<IncomeModel> createIncome(IncomeModel income) async {
    try {
      final id = await _database
          .into(_database.incomeTable)
          .insert(income.toDrift());

      final created = await getIncomeById(id);
      if (created == null) {
        throw DatabaseException('Failed to retrieve created income');
      }

      return created;
    } catch (e) {
      throw DatabaseException('Failed to create income: $e');
    }
  }

  /// Update existing income
  Future<IncomeModel> updateIncome(IncomeModel income) async {
    try {
      if (income.id == null) {
        throw DatabaseException('Cannot update income without ID');
      }

      final updated = await (_database.update(
        _database.incomeTable,
      )..where((tbl) => tbl.id.equals(income.id!))).write(income.toDrift());

      if (updated == 0) {
        throw DatabaseException('Income not found or not updated');
      }

      final result = await getIncomeById(income.id!);
      if (result == null) {
        throw DatabaseException('Failed to retrieve updated income');
      }

      return result;
    } catch (e) {
      throw DatabaseException('Failed to update income: $e');
    }
  }

  /// Soft delete income
  Future<void> deleteIncome(int id) async {
    try {
      final updated =
          await (_database.update(
            _database.incomeTable,
          )..where((tbl) => tbl.id.equals(id))).write(
            IncomeTableCompanion(
              isDeleted: const Value(true),
              deletedAt: Value(DateTime.now()),
              updatedAt: Value(DateTime.now()),
            ),
          );

      if (updated == 0) {
        throw DatabaseException('Income not found');
      }
    } catch (e) {
      throw DatabaseException('Failed to delete income: $e');
    }
  }

  /// Get income summary for date range
  Future<Map<String, double>> getSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final query = _database.select(_database.incomeTable)
        ..where((tbl) => tbl.isDeleted.equals(false));

      if (startDate != null && endDate != null) {
        query.where((tbl) => tbl.date.isBetweenValues(startDate, endDate));
      }

      final results = await query.get();
      final incomes = results.map((row) => IncomeModel.fromDrift(row)).toList();

      double totalInitialCapital = 0;
      double totalFinalResult = 0;
      double totalNetIncome = 0;
      int totalMileage = 0;

      for (final income in incomes) {
        totalInitialCapital += income.initialCapital;
        totalFinalResult += income.finalResult;
        totalNetIncome += income.netIncome;
        totalMileage += income.mileage;
      }

      return {
        'totalInitialCapital': totalInitialCapital,
        'totalFinalResult': totalFinalResult,
        'totalNetIncome': totalNetIncome,
        'totalMileage': totalMileage.toDouble(),
        'count': incomes.length.toDouble(),
      };
    } catch (e) {
      throw DatabaseException('Failed to get summary: $e');
    }
  }

  /// Search incomes
  Future<List<IncomeModel>> searchIncomes(String query) async {
    try {
      // For now, we'll search by date range or other criteria
      // Since we don't have text fields to search in the income model
      final results = await _database.select(_database.incomeTable).get();
      return results
          .map((row) => IncomeModel.fromDrift(row))
          .where((income) => !income.isDeleted)
          .toList();
    } catch (e) {
      throw DatabaseException('Failed to search incomes: $e');
    }
  }

  /// Get unsynced incomes
  Future<List<IncomeModel>> getUnsyncedIncomes() async {
    try {
      final query = _database.select(_database.incomeTable)
        ..where((tbl) => tbl.isSynced.equals(false));

      final results = await query.get();
      return results.map((row) => IncomeModel.fromDrift(row)).toList();
    } catch (e) {
      throw DatabaseException('Failed to get unsynced incomes: $e');
    }
  }

  /// Mark income as synced by UUID
  Future<bool> markAsSynced(String uuid) async {
    try {
      final rowsAffected =
          await (_database.update(_database.incomeTable)
                ..where((tbl) => tbl.uuid.equals(uuid)))
              .write(const IncomeTableCompanion(isSynced: Value(true)));
      return rowsAffected > 0;
    } catch (e) {
      throw DatabaseException('Failed to mark income as synced: $e');
    }
  }

  /// Soft delete income by setting isDeleted flag
  Future<void> softDeleteIncome(int id) async {
    try {
      await (_database.update(
        _database.incomeTable,
      )..where((tbl) => tbl.id.equals(id))).write(
        IncomeTableCompanion(
          isDeleted: const Value(true),
          deletedAt: Value(DateTime.now()),
        ),
      );
    } catch (e) {
      throw DatabaseException('Failed to soft delete income: $e');
    }
  }

  /// Get all incomes including deleted ones
  Future<List<IncomeModel>> getAllIncludingDeleted() async {
    try {
      final query = _database.select(_database.incomeTable)
        ..orderBy([(t) => OrderingTerm.desc(t.date)]);

      final results = await query.get();
      return results.map((row) => IncomeModel.fromDrift(row)).toList();
    } catch (e) {
      throw DatabaseException(
        'Failed to get all incomes including deleted: $e',
      );
    }
  }

  /// Restore soft deleted income
  Future<void> restoreIncome(int id) async {
    try {
      await (_database.update(
        _database.incomeTable,
      )..where((tbl) => tbl.id.equals(id))).write(
        const IncomeTableCompanion(
          isDeleted: Value(false),
          deletedAt: Value.absent(),
        ),
      );
    } catch (e) {
      throw DatabaseException('Failed to restore income: $e');
    }
  }

  /// Get count of incomes
  Future<int> getCount({
    DateTime? startDate,
    DateTime? endDate,
    bool includeDeleted = false,
  }) async {
    try {
      var query = _database.selectOnly(_database.incomeTable)
        ..addColumns([_database.incomeTable.id.count()]);

      if (!includeDeleted) {
        query = query..where(_database.incomeTable.isDeleted.equals(false));
      }

      if (startDate != null && endDate != null) {
        query = query
          ..where(
            _database.incomeTable.date.isBetweenValues(startDate, endDate),
          );
      }

      final result = await query.getSingle();
      return result.read(_database.incomeTable.id.count()) ?? 0;
    } catch (e) {
      throw DatabaseException('Failed to get income count: $e');
    }
  }

  /// Get highest mileage from all records
  Future<int> getHighestMileage() async {
    try {
      final query = _database.selectOnly(_database.incomeTable)
        ..addColumns([_database.incomeTable.finalMileage.max()])
        ..where(_database.incomeTable.isDeleted.equals(false));

      final result = await query.getSingle();
      return result.read(_database.incomeTable.finalMileage.max()) ?? 0;
    } catch (e) {
      throw DatabaseException('Failed to get highest mileage: $e');
    }
  }

  /// Get incomplete income records (missing final values)
  Future<List<IncomeModel>> getIncompleteIncomes() async {
    try {
      final query = _database.select(_database.incomeTable)
        ..where(
          (tbl) =>
              tbl.isDeleted.equals(false) &
              (tbl.finalMileage.equals(0) |
                  (tbl.finalGopay.equals(0) &
                      tbl.finalBca.equals(0) &
                      tbl.finalCash.equals(0) &
                      tbl.finalOvo.equals(0) &
                      tbl.finalBri.equals(0) &
                      tbl.finalRekpon.equals(0))),
        )
        ..orderBy([(t) => OrderingTerm.desc(t.date)]);

      final results = await query.get();
      return results.map((row) => IncomeModel.fromDrift(row)).toList();
    } catch (e) {
      throw DatabaseException('Failed to get incomplete incomes: $e');
    }
  }
}
