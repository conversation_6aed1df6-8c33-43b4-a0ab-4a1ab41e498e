import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../../core/datasources/app_database.dart';
import '../../data/datasources/income_local_datasource.dart';
import '../../data/repositories/income_repository_impl.dart';
import '../../domain/repositories/income_repository.dart';
import '../../domain/use_cases/calculate_net_income.dart';

// Database provider
final databaseProvider = Provider<AppDatabase>((ref) {
  return AppDatabase();
});

// Data source provider
final incomeLocalDatasourceProvider = Provider<IncomeLocalDatasource>((ref) {
  final database = ref.watch(databaseProvider);
  return IncomeLocalDatasource(database);
});

// Repository provider
final incomeRepositoryProvider = Provider<IIncomeRepository>((ref) {
  final localDatasource = ref.watch(incomeLocalDatasourceProvider);
  return IncomeRepositoryImpl(localDatasource);
});

// Use case providers
final calculateNetIncomeProvider = Provider<CalculateNetIncome>((ref) {
  final repository = ref.watch(incomeRepositoryProvider);
  return CalculateNetIncome();
});

// State providers for UI
final selectedDateProvider = StateProvider<DateTime>((ref) {
  return DateTime.now();
});

final isLoadingProvider = StateProvider<bool>((ref) {
  return false;
});

final errorMessageProvider = StateProvider<String?>((ref) {
  return null;
});

// Income list provider with pagination
final incomeListProvider =
    FutureProvider.family<List<dynamic>, Map<String, dynamic>>((
      ref,
      params,
    ) async {
      final repository = ref.watch(incomeRepositoryProvider);

      final result = await repository.getPaginated(
        page: (params['offset'] ?? 0) ~/ (params['limit'] ?? 20) + 1,
        limit: params['limit'] ?? 20,
        startDate: params['startDate'],
        endDate: params['endDate'],
      );

      return result.fold(
        (failure) => throw Exception(failure.message),
        (incomes) => incomes,
      );
    });

// Income summary provider
final incomeSummaryProvider =
    FutureProvider.family<dynamic, Map<String, DateTime?>>((
      ref,
      dateRange,
    ) async {
      final repository = ref.watch(incomeRepositoryProvider);

      final result = await repository.getSummary(
        startDate: dateRange['startDate'],
        endDate: dateRange['endDate'],
      );

      return result.fold(
        (failure) => throw Exception(failure.message),
        (summary) => summary,
      );
    });

// Single income provider
final incomeByIdProvider = FutureProvider.family<dynamic, int>((ref, id) async {
  final repository = ref.watch(incomeRepositoryProvider);

  final result = await repository.getById(id);

  return result.fold(
    (failure) => throw Exception(failure.message),
    (income) => income,
  );
});

// Date exists provider
final dateExistsProvider = FutureProvider.family<bool, DateTime>((
  ref,
  date,
) async {
  final repository = ref.watch(incomeRepositoryProvider);

  final result = await repository.checkDateExists(date);

  return result.fold(
    (failure) => throw Exception(failure.message),
    (exists) => exists,
  );
});

// Income trends provider
final incomeTrendsProvider =
    FutureProvider.family<List<dynamic>, Map<String, dynamic>>((
      ref,
      params,
    ) async {
      final repository = ref.watch(incomeRepositoryProvider);

      final result = await repository.getForTrends(
        startDate: params['startDate'],
        endDate: params['endDate'],
        limit: params['limit'],
      );

      return result.fold(
        (failure) => throw Exception(failure.message),
        (trends) => trends,
      );
    });

// Search incomes provider
final searchIncomesProvider = FutureProvider.family<List<dynamic>, String>((
  ref,
  query,
) async {
  final repository = ref.watch(incomeRepositoryProvider);

  final result = await repository.search(query: query);

  return result.fold(
    (failure) => throw Exception(failure.message),
    (incomes) => incomes,
  );
});

// Unsynced incomes provider
final unsyncedIncomesProvider = FutureProvider<List<dynamic>>((ref) async {
  final repository = ref.watch(incomeRepositoryProvider);

  final result = await repository.getUnsyncedEntities();

  return result.fold(
    (failure) => throw Exception(failure.message),
    (incomes) => incomes,
  );
});

// Form state providers
final initialMileageProvider = StateProvider<String>((ref) => '');
final finalMileageProvider = StateProvider<String>((ref) => '');

// Initial balance providers
final initialGopayProvider = StateProvider<String>((ref) => '');
final initialBcaProvider = StateProvider<String>((ref) => '');
final initialCashProvider = StateProvider<String>((ref) => '');
final initialOvoProvider = StateProvider<String>((ref) => '');
final initialBriProvider = StateProvider<String>((ref) => '');
final initialRekponProvider = StateProvider<String>((ref) => '');

// Final balance providers
final finalGopayProvider = StateProvider<String>((ref) => '');
final finalBcaProvider = StateProvider<String>((ref) => '');
final finalCashProvider = StateProvider<String>((ref) => '');
final finalOvoProvider = StateProvider<String>((ref) => '');
final finalBriProvider = StateProvider<String>((ref) => '');
final finalRekponProvider = StateProvider<String>((ref) => '');

// Computed providers for real-time calculations
final currentInitialCapitalProvider = Provider<double>((ref) {
  final gopay = double.tryParse(ref.watch(initialGopayProvider)) ?? 0;
  final bca = double.tryParse(ref.watch(initialBcaProvider)) ?? 0;
  final cash = double.tryParse(ref.watch(initialCashProvider)) ?? 0;
  final ovo = double.tryParse(ref.watch(initialOvoProvider)) ?? 0;
  final bri = double.tryParse(ref.watch(initialBriProvider)) ?? 0;
  final rekpon = double.tryParse(ref.watch(initialRekponProvider)) ?? 0;

  return gopay + bca + cash + ovo + bri + rekpon;
});

final currentFinalResultProvider = Provider<double>((ref) {
  final gopay = double.tryParse(ref.watch(finalGopayProvider)) ?? 0;
  final bca = double.tryParse(ref.watch(finalBcaProvider)) ?? 0;
  final cash = double.tryParse(ref.watch(finalCashProvider)) ?? 0;
  final ovo = double.tryParse(ref.watch(finalOvoProvider)) ?? 0;
  final bri = double.tryParse(ref.watch(finalBriProvider)) ?? 0;
  final rekpon = double.tryParse(ref.watch(finalRekponProvider)) ?? 0;

  return gopay + bca + cash + ovo + bri + rekpon;
});

final currentNetIncomeProvider = Provider<double>((ref) {
  final initialCapital = ref.watch(currentInitialCapitalProvider);
  final finalResult = ref.watch(currentFinalResultProvider);

  return finalResult - initialCapital;
});

final currentMileageProvider = Provider<int>((ref) {
  final initial = int.tryParse(ref.watch(initialMileageProvider)) ?? 0;
  final final_ = int.tryParse(ref.watch(finalMileageProvider)) ?? 0;

  return final_ - initial;
});
