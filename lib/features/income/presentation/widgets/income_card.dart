import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../../core/utils/calculations.dart';
import '../../domain/entities/income.dart';

/// Card widget displaying income record information
class IncomeCard extends StatelessWidget {
  final Income income;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const IncomeCard({
    super.key,
    required this.income,
    this.onTap,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final netIncomeColor = income.netIncome >= 0
        ? AppColors.success
        : AppColors.error;
    final netIncomeIcon = income.netIncome >= 0
        ? Icons.trending_up
        : Icons.trending_down;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with date and actions
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 14,
                          color: AppColors.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          DateHelper.formatForDisplay(income.date),
                          style: TextStyle(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  if (!income.isSynced)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.warning.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.sync_disabled,
                            size: 12,
                            color: AppColors.warning,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Unsynced',
                            style: TextStyle(
                              color: AppColors.warning,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (onDelete != null) ...[
                    const SizedBox(width: 8),
                    IconButton(
                      icon: const Icon(Icons.delete_outline),
                      color: AppColors.error,
                      iconSize: 20,
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: EdgeInsets.zero,
                      onPressed: onDelete,
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 12),

              // Financial summary row
              Row(
                children: [
                  Expanded(
                    child: _buildFinancialItem(
                      'Initial Capital',
                      income.initialCapital,
                      AppColors.primary,
                      Icons.savings,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildFinancialItem(
                      'Final Result',
                      income.finalResult,
                      AppColors.secondary,
                      Icons.account_balance,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Net income and mileage row
              Row(
                children: [
                  Expanded(
                    child: _buildFinancialItem(
                      'Net Income',
                      income.netIncome,
                      netIncomeColor,
                      netIncomeIcon,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildMileageItem(
                      'Distance',
                      income.mileage,
                      AppColors.info,
                      Icons.speed,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Balance breakdown (collapsible)
              ExpansionTile(
                title: const Text(
                  'Balance Details',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
                tilePadding: EdgeInsets.zero,
                childrenPadding: const EdgeInsets.only(top: 8),
                children: [_buildBalanceBreakdown()],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFinancialItem(
    String label,
    double value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            Calculations.formatCurrency(value),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMileageItem(
    String label,
    int value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            Calculations.formatMileage(value),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceBreakdown() {
    return Column(
      children: [
        // Initial balances
        _buildBalanceSection('Initial Balances', [
          _buildBalanceRow('Gopay', income.initialGopay),
          _buildBalanceRow('BCA', income.initialBca),
          _buildBalanceRow('Cash', income.initialCash),
          _buildBalanceRow('OVO', income.initialOvo),
          _buildBalanceRow('BRI', income.initialBri),
          _buildBalanceRow('Rekpon', income.initialRekpon),
        ]),

        const SizedBox(height: 12),

        // Final balances
        _buildBalanceSection('Final Balances', [
          _buildBalanceRow('Gopay', income.finalGopay),
          _buildBalanceRow('BCA', income.finalBca),
          _buildBalanceRow('Cash', income.finalCash),
          _buildBalanceRow('OVO', income.finalOvo),
          _buildBalanceRow('BRI', income.finalBri),
          _buildBalanceRow('Rekpon', income.finalRekpon),
        ]),
      ],
    );
  }

  Widget _buildBalanceSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w600,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildBalanceRow(String label, double value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontSize: 12)),
          Text(
            Calculations.formatCurrency(value),
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }
}
