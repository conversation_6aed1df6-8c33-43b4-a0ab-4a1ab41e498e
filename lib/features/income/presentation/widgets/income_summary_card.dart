import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/calculations.dart';
import '../../domain/repositories/income_repository.dart';

/// Widget displaying income summary statistics
class IncomeSummaryCard extends StatelessWidget {
  final IncomeSummary summary;

  const IncomeSummaryCard({super.key, required this.summary});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.analytics, color: AppColors.primary, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Income Summary',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.info.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${summary.count} records',
                    style: TextStyle(
                      color: AppColors.info,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Main metrics row
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Total Net Income',
                    summary.totalNetIncome,
                    summary.totalNetIncome >= 0
                        ? AppColors.success
                        : AppColors.error,
                    summary.totalNetIncome >= 0
                        ? Icons.trending_up
                        : Icons.trending_down,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    'Average Income',
                    summary.averageNetIncome,
                    summary.averageNetIncome >= 0
                        ? AppColors.success
                        : AppColors.error,
                    Icons.show_chart,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Capital and result row
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Total Initial Capital',
                    summary.totalInitialCapital,
                    AppColors.primary,
                    Icons.savings,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    'Total Final Result',
                    summary.totalFinalResult,
                    AppColors.secondary,
                    Icons.account_balance,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Mileage metrics row
            Row(
              children: [
                Expanded(
                  child: _buildMileageCard(
                    'Total Distance',
                    summary.totalMileage,
                    AppColors.info,
                    Icons.speed,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMileageCard(
                    'Average Distance',
                    summary.averageMileage.round(),
                    AppColors.info,
                    Icons.route,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Performance indicators
            if (summary.count > 1) ...[
              const Divider(),
              const SizedBox(height: 12),
              _buildPerformanceIndicators(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    String title,
    double value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 11,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            Calculations.formatCurrency(value),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMileageCard(
    String title,
    int value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 11,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            Calculations.formatMileage(value),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceIndicators() {
    final profitabilityRate = summary.totalInitialCapital > 0
        ? (summary.totalNetIncome / summary.totalInitialCapital) * 100
        : 0.0;

    final incomePerKm = summary.totalMileage > 0
        ? summary.totalNetIncome / summary.totalMileage
        : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Performance Indicators',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildIndicatorCard(
                'Profitability Rate',
                '${profitabilityRate.toStringAsFixed(1)}%',
                profitabilityRate >= 0 ? AppColors.success : AppColors.error,
                Icons.percent,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildIndicatorCard(
                'Income per KM',
                Calculations.formatCurrency(incomePerKm),
                incomePerKm >= 0 ? AppColors.success : AppColors.error,
                Icons.timeline,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildIndicatorCard(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withOpacity(0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 14, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 10,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
