import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../../core/widgets/base_form_screen.dart';
import '../../../../core/widgets/currency_input_field.dart';
import '../../../../core/widgets/mileage_input.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/date_helper.dart';
import '../../domain/entities/income.dart';
import '../providers/income_providers.dart';

/// Form screen for creating and editing income records
class IncomeFormScreen extends BaseFormScreen<Income> {
  const IncomeFormScreen({super.key, super.entity});

  @override
  IncomeFormScreenState createState() => IncomeFormScreenState();
}

class IncomeFormScreenState
    extends BaseFormScreenState<Income, IncomeFormScreen>
    with HookConsumerStateMixin {
  // Controllers for form fields
  late TextEditingController _initialMileageController;
  late TextEditingController _finalMileageController;

  // Initial balance controllers
  late TextEditingController _initialGopayController;
  late TextEditingController _initialBcaController;
  late TextEditingController _initialCashController;
  late TextEditingController _initialOvoController;
  late TextEditingController _initialBriController;
  late TextEditingController _initialRekponController;

  // Final balance controllers
  late TextEditingController _finalGopayController;
  late TextEditingController _finalBcaController;
  late TextEditingController _finalCashController;
  late TextEditingController _finalOvoController;
  late TextEditingController _finalBriController;
  late TextEditingController _finalRekponController;

  @override
  DateTime getInitialDate() {
    return widget.entity?.date ?? DateTime.now();
  }

  @override
  void initializeControllers() {
    final income = widget.entity;

    _initialMileageController = TextEditingController(
      text: income?.initialMileage.toString() ?? '',
    );
    _finalMileageController = TextEditingController(
      text: income?.finalMileage.toString() ?? '',
    );

    // Initial balances
    _initialGopayController = TextEditingController(
      text: income?.initialGopay.toString() ?? '',
    );
    _initialBcaController = TextEditingController(
      text: income?.initialBca.toString() ?? '',
    );
    _initialCashController = TextEditingController(
      text: income?.initialCash.toString() ?? '',
    );
    _initialOvoController = TextEditingController(
      text: income?.initialOvo.toString() ?? '',
    );
    _initialBriController = TextEditingController(
      text: income?.initialBri.toString() ?? '',
    );
    _initialRekponController = TextEditingController(
      text: income?.initialRekpon.toString() ?? '',
    );

    // Final balances
    _finalGopayController = TextEditingController(
      text: income?.finalGopay.toString() ?? '',
    );
    _finalBcaController = TextEditingController(
      text: income?.finalBca.toString() ?? '',
    );
    _finalCashController = TextEditingController(
      text: income?.finalCash.toString() ?? '',
    );
    _finalOvoController = TextEditingController(
      text: income?.finalOvo.toString() ?? '',
    );
    _finalBriController = TextEditingController(
      text: income?.finalBri.toString() ?? '',
    );
    _finalRekponController = TextEditingController(
      text: income?.finalRekpon.toString() ?? '',
    );
  }

  @override
  void disposeControllers() {
    _initialMileageController.dispose();
    _finalMileageController.dispose();

    _initialGopayController.dispose();
    _initialBcaController.dispose();
    _initialCashController.dispose();
    _initialOvoController.dispose();
    _initialBriController.dispose();
    _initialRekponController.dispose();

    _finalGopayController.dispose();
    _finalBcaController.dispose();
    _finalCashController.dispose();
    _finalOvoController.dispose();
    _finalBriController.dispose();
    _finalRekponController.dispose();
  }

  @override
  String getFormTitle() {
    return isEditing ? 'Edit Income Record' : 'Add Income Record';
  }

  @override
  Future<bool> checkDateExists() async {
    final repository = ref.read(incomeRepositoryProvider);
    final result = await repository.existsForDate(selectedDate);

    return result.fold(
      (failure) => false, // Assume doesn't exist on error
      (exists) => exists,
    );
  }

  @override
  Future<void> submitForm() async {
    final repository = ref.read(incomeRepositoryProvider);

    final income = Income(
      id: widget.entity?.id,
      date: selectedDate,
      initialMileage: int.parse(_initialMileageController.text),
      finalMileage: int.parse(_finalMileageController.text),
      initialGopay: double.parse(
        _initialGopayController.text.isEmpty
            ? '0'
            : _initialGopayController.text,
      ),
      initialBca: double.parse(
        _initialBcaController.text.isEmpty ? '0' : _initialBcaController.text,
      ),
      initialCash: double.parse(
        _initialCashController.text.isEmpty ? '0' : _initialCashController.text,
      ),
      initialOvo: double.parse(
        _initialOvoController.text.isEmpty ? '0' : _initialOvoController.text,
      ),
      initialBri: double.parse(
        _initialBriController.text.isEmpty ? '0' : _initialBriController.text,
      ),
      initialRekpon: double.parse(
        _initialRekponController.text.isEmpty
            ? '0'
            : _initialRekponController.text,
      ),
      finalGopay: double.parse(
        _finalGopayController.text.isEmpty ? '0' : _finalGopayController.text,
      ),
      finalBca: double.parse(
        _finalBcaController.text.isEmpty ? '0' : _finalBcaController.text,
      ),
      finalCash: double.parse(
        _finalCashController.text.isEmpty ? '0' : _finalCashController.text,
      ),
      finalOvo: double.parse(
        _finalOvoController.text.isEmpty ? '0' : _finalOvoController.text,
      ),
      finalBri: double.parse(
        _finalBriController.text.isEmpty ? '0' : _finalBriController.text,
      ),
      finalRekpon: double.parse(
        _finalRekponController.text.isEmpty ? '0' : _finalRekponController.text,
      ),
      uuid: widget.entity?.uuid,
      isDeleted: widget.entity?.isDeleted ?? false,
      isSynced: false, // Mark as unsynced when modified
      createdAt: widget.entity?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
      deletedAt: widget.entity?.deletedAt,
    );

    final result = isEditing
        ? await repository.updateIncome(income)
        : await repository.createIncome(income);

    result.fold((failure) => throw Exception(failure.message), (savedIncome) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isEditing ? 'Income record updated' : 'Income record created',
            ),
            backgroundColor: AppColors.success,
          ),
        );
      }
    });
  }

  @override
  List<Widget> buildFormFields() {
    // Calculate real-time values
    final initialCapital = _calculateInitialCapital();
    final finalResult = _calculateFinalResult();
    final netIncome = finalResult - initialCapital;
    final mileage = _calculateMileage();

    return [
      // Mileage section
      buildSectionTitle('Mileage', icon: Icons.speed),
      MileageInput(
        label: 'Initial Mileage',
        controller: _initialMileageController,
        isRequired: true,
        onChanged: (_) => setState(() {}),
      ),
      MileageInput(
        label: 'Final Mileage',
        controller: _finalMileageController,
        isRequired: true,
        onChanged: (_) => setState(() {}),
      ),

      // Mileage difference display
      if (mileage > 0)
        Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: AppColors.success.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppColors.success.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              const Icon(Icons.trending_up, color: AppColors.success),
              const SizedBox(width: 8),
              Text(
                'Distance: $mileage km',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.success,
                ),
              ),
            ],
          ),
        ),

      const SizedBox(height: 16),

      // Initial balances section
      buildSectionTitle('Initial Balances', icon: Icons.account_balance_wallet),
      CurrencyInputField(
        label: 'Gopay',
        controller: _initialGopayController,
        onChanged: (_) => setState(() {}),
      ),
      CurrencyInputField(
        label: 'BCA',
        controller: _initialBcaController,
        onChanged: (_) => setState(() {}),
      ),
      CurrencyInputField(
        label: 'Cash',
        controller: _initialCashController,
        onChanged: (_) => setState(() {}),
      ),
      CurrencyInputField(
        label: 'OVO',
        controller: _initialOvoController,
        onChanged: (_) => setState(() {}),
      ),
      CurrencyInputField(
        label: 'BRI',
        controller: _initialBriController,
        onChanged: (_) => setState(() {}),
      ),
      CurrencyInputField(
        label: 'Rekpon',
        controller: _initialRekponController,
        onChanged: (_) => setState(() {}),
      ),

      // Initial capital display
      _buildCalculationCard(
        'Initial Capital',
        initialCapital,
        AppColors.primary,
        Icons.savings,
      ),

      const SizedBox(height: 16),

      // Final balances section
      buildSectionTitle('Final Balances', icon: Icons.account_balance),
      CurrencyInputField(
        label: 'Gopay',
        controller: _finalGopayController,
        onChanged: (_) => setState(() {}),
      ),
      CurrencyInputField(
        label: 'BCA',
        controller: _finalBcaController,
        onChanged: (_) => setState(() {}),
      ),
      CurrencyInputField(
        label: 'Cash',
        controller: _finalCashController,
        onChanged: (_) => setState(() {}),
      ),
      CurrencyInputField(
        label: 'OVO',
        controller: _finalOvoController,
        onChanged: (_) => setState(() {}),
      ),
      CurrencyInputField(
        label: 'BRI',
        controller: _finalBriController,
        onChanged: (_) => setState(() {}),
      ),
      CurrencyInputField(
        label: 'Rekpon',
        controller: _finalRekponController,
        onChanged: (_) => setState(() {}),
      ),

      // Final result display
      _buildCalculationCard(
        'Final Result',
        finalResult,
        AppColors.secondary,
        Icons.account_balance,
      ),

      const SizedBox(height: 16),

      // Net income display
      _buildCalculationCard(
        'Net Income',
        netIncome,
        netIncome >= 0 ? AppColors.success : AppColors.error,
        netIncome >= 0 ? Icons.trending_up : Icons.trending_down,
      ),
    ];
  }

  Widget _buildCalculationCard(
    String title,
    double value,
    Color color,
    IconData icon,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Rp ${value.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  double _calculateInitialCapital() {
    final gopay = double.tryParse(_initialGopayController.text) ?? 0;
    final bca = double.tryParse(_initialBcaController.text) ?? 0;
    final cash = double.tryParse(_initialCashController.text) ?? 0;
    final ovo = double.tryParse(_initialOvoController.text) ?? 0;
    final bri = double.tryParse(_initialBriController.text) ?? 0;
    final rekpon = double.tryParse(_initialRekponController.text) ?? 0;

    return gopay + bca + cash + ovo + bri + rekpon;
  }

  double _calculateFinalResult() {
    final gopay = double.tryParse(_finalGopayController.text) ?? 0;
    final bca = double.tryParse(_finalBcaController.text) ?? 0;
    final cash = double.tryParse(_finalCashController.text) ?? 0;
    final ovo = double.tryParse(_finalOvoController.text) ?? 0;
    final bri = double.tryParse(_finalBriController.text) ?? 0;
    final rekpon = double.tryParse(_finalRekponController.text) ?? 0;

    return gopay + bca + cash + ovo + bri + rekpon;
  }

  int _calculateMileage() {
    final initial = int.tryParse(_initialMileageController.text) ?? 0;
    final final_ = int.tryParse(_finalMileageController.text) ?? 0;

    return final_ - initial;
  }
}
