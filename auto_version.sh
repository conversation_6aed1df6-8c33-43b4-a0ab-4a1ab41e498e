#!/bin/bash

# <PERSON>ript to automatically update version and changelog based on commits
# This script analyzes git commits and updates the version and changelog accordingly

# Default values
DRY_RUN=false
FORCE_UPDATE=false
BUMP_TYPE=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    --force)
      FORCE_UPDATE=true
      shift
      ;;
    --patch|--minor|--major)
      BUMP_TYPE="${1:2}" # Remove the -- prefix
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: $0 [--dry-run] [--force] [--patch|--minor|--major]"
      exit 1
      ;;
  esac
done

# Build the command arguments
ARGS=()
if [ "$DRY_RUN" = true ]; then
  ARGS+=("--dry-run")
fi

if [ "$FORCE_UPDATE" = true ] || [ -n "$BUMP_TYPE" ]; then
  ARGS+=("--force")
  if [ -n "$BUMP_TYPE" ]; then
    ARGS+=("$BUMP_TYPE")
  fi
fi

# Run the Dart script
dart .github/scripts/auto_version.dart "${ARGS[@]}"
