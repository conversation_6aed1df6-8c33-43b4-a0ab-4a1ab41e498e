include: package:flutter_lints/flutter.yaml

linter:
  rules:
    # Style rules
    - camel_case_types
    - camel_case_extensions
    - library_names
    - file_names
    - library_prefixes
    - non_constant_identifier_names
    - constant_identifier_names
    - directives_ordering
    - package_prefixed_library_names
    - prefer_const_constructors
    - prefer_const_declarations
    - prefer_final_fields
    - prefer_final_locals
    - avoid_print
    - avoid_empty_else
    - avoid_relative_lib_imports
    - avoid_slow_async_io
    - avoid_types_as_parameter_names
    - avoid_unused_constructor_parameters
    - cancel_subscriptions
    - close_sinks
    - comment_references
    - control_flow_in_finally
    - empty_catches
    - empty_constructor_bodies
    - empty_statements
    - hash_and_equals
    - implementation_imports
    - literal_only_boolean_expressions
    - no_adjacent_strings_in_list
    - no_duplicate_case_values
    - prefer_void_to_null
    - test_types_in_equals
    - throw_in_finally
    - unnecessary_statements
    - unrelated_type_equality_checks
    - use_key_in_widget_constructors
    - valid_regexps

analyzer:
  errors:
    # Treat missing required parameters as an error
    missing_required_param: error
    # Treat missing returns as an error
    missing_return: error
    # Allow having TODOs in the code
    todo: ignore
